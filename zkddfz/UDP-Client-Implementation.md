# UDP客户端功能实现文档

## 📋 功能概述

根据需求，将原有的UDP广播机制修改为UDP客户端直接发送到指定服务端的方式。当WebSocket接收到车辆状态更新数据时，不再使用广播形式，而是直接通过UDP客户端将数据实时发送到指定的UDP服务端。

## 🔧 实现的功能

### 1. **UDP客户端 (UdpClient)**
- 连接到指定的UDP服务端
- 支持车辆数据列表和单个车辆数据发送
- 支持原始数据发送
- 自动初始化和资源管理

### 2. **WebSocket集成**
- 在车辆状态更新处理中自动触发UDP客户端发送
- 替代原有的广播机制
- 保持原有的数据格式和处理逻辑

### 3. **管理接口**
- REST API管理UDP客户端状态
- 配置信息获取和连接测试
- 使用说明和帮助信息

## 📁 新增/修改的文件

### 新增文件:
1. **`UdpClient.java`** - UDP客户端核心类
2. **`UdpClientController.java`** - UDP客户端管理REST API
3. **`test-udp-server.py`** - Python UDP服务端测试工具
4. **`UDP-Client-Implementation.md`** - 本文档

### 修改文件:
1. **`RouteWebSocketHandler.java`** - 集成UDP客户端功能
2. **`application-dev.properties`** - 添加UDP客户端配置
3. **`application-prod.properties`** - 添加UDP客户端配置

## ⚙️ 配置参数

在 `application-dev.properties` 和 `application-prod.properties` 中添加了以下配置:

```properties
# UDP客户端配置 - 发送车辆数据到指定服务端
udp.client.target.host=127.0.0.1     # 目标UDP服务端主机地址
udp.client.target.port=9092           # 目标UDP服务端端口
udp.client.enabled=true               # 启用UDP客户端功能

# 保留广播配置以兼容现有功能
udp.broadcast.enabled=false           # 禁用UDP广播功能
```

## 🔄 工作流程

1. **WebSocket连接建立** → 初始化UDP客户端
2. **接收车辆状态数据** → 解析车辆数据列表
3. **处理车辆状态更新** → 记录日志并触发UDP客户端发送
4. **UDP客户端发送** → 直接发送到指定的UDP服务端
5. **连接关闭** → 清理UDP客户端资源

## 📡 数据格式

### WebSocket接收格式 (保持不变):
```json
[
  {
    "device_id": "PCC_12345",
    "position": {"x": 100, "y": 0, "z": 200},
    "rotation": {"x": 0, "y": 90, "z": 0},
    "duration": 0,
    "power": 85,
    "consume": 15,
    "type": "PCC"
  }
]
```

### UDP发送格式 (与接收格式相同):
数据以JSON格式发送到指定的UDP服务端，格式与WebSocket接收的格式完全一致。

## 🚀 使用方法

### 1. 启动应用
```bash
# 启动Spring Boot应用
./mvnw spring-boot:run
```

### 2. 启动UDP服务端测试工具
```bash
# 启动Python测试服务端
python test-udp-server.py
```

### 3. 测试UDP客户端
```bash
# 获取UDP客户端状态
curl http://localhost:8080/api/udp-client/status

# 获取UDP客户端配置
curl http://localhost:8080/api/udp-client/config

# 测试UDP客户端连接
curl -X POST http://localhost:8080/api/udp-client/test
```

## 🔍 API接口

### UDP客户端管理接口:
- `GET /api/udp-client/status` - 获取UDP客户端状态
- `GET /api/udp-client/config` - 获取UDP客户端配置信息
- `GET /api/udp-client/help` - 获取使用说明
- `POST /api/udp-client/test` - 测试UDP客户端连接

## 📊 主要变更

### 1. **RouteWebSocketHandler.java**
- 添加 `UdpClient` 依赖注入
- 新增 `initializeUdpClientIfNeeded()` 方法
- 新增 `shutdownUdpClient()` 方法
- 新增 `sendVehicleDataViaUdpClient()` 方法
- 修改 `handleVehicleStatusUpdate()` 调用UDP客户端发送

### 2. **UdpClient.java (新增)**
- 实现UDP客户端连接和数据发送功能
- 支持多种数据格式发送
- 自动资源管理和错误处理

### 3. **配置文件**
- 添加UDP客户端目标服务端配置
- 禁用UDP广播功能
- 保持向后兼容性

## 🧪 测试验证

1. **启动应用和测试服务端**
2. **连接WebSocket客户端发送车辆数据**
3. **观察UDP服务端接收到的数据**
4. **验证数据格式和内容正确性**

## 📝 注意事项

1. **向后兼容**: 保留了原有的UDP广播功能，只是默认禁用
2. **配置灵活**: 可以通过配置文件轻松切换目标服务端
3. **错误处理**: 完善的异常处理和日志记录
4. **资源管理**: 自动初始化和清理UDP客户端资源
5. **测试工具**: 提供Python测试服务端便于验证功能

## 🔧 故障排除

1. **UDP客户端连接失败**: 检查目标服务端是否启动和配置是否正确
2. **数据发送失败**: 查看应用日志中的错误信息
3. **配置不生效**: 确认配置文件路径和格式正确
4. **端口冲突**: 修改配置中的端口号避免冲突
