#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UDP服务端测试工具
用于接收UDP客户端发送的车辆数据
"""

import socket
import json
import threading
import time
from datetime import datetime

class UdpTestServer:
    def __init__(self, host='127.0.0.1', port=9092, buffer_size=8192):
        self.host = host
        self.port = port
        self.buffer_size = buffer_size
        self.socket = None
        self.running = False
        self.received_count = 0
        
    def start_server(self):
        """启动UDP服务端"""
        try:
            # 创建UDP socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            # 绑定到指定地址和端口
            self.socket.bind((self.host, self.port))
            self.running = True
            
            print(f"🚀 UDP测试服务端启动成功")
            print(f"📡 监听地址: {self.host}:{self.port}")
            print(f"📦 缓冲区大小: {self.buffer_size} 字节")
            print("-" * 80)
            print("等待接收车辆数据...")
            print("-" * 80)
            
            while self.running:
                try:
                    # 接收UDP数据
                    data, addr = self.socket.recvfrom(self.buffer_size)
                    self.handle_received_data(data, addr)
                    
                except socket.timeout:
                    continue
                except socket.error as e:
                    if self.running:
                        print(f"❌ Socket错误: {e}")
                    break
                    
        except Exception as e:
            print(f"❌ 启动UDP服务端失败: {e}")
        finally:
            self.cleanup()
    
    def handle_received_data(self, data, addr):
        """处理接收到的数据"""
        try:
            self.received_count += 1
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            
            # 解码数据
            message = data.decode('utf-8')
            
            print(f"📨 [{timestamp}] 收到数据 #{self.received_count}")
            print(f"📍 来源地址: {addr[0]}:{addr[1]}")
            print(f"📏 数据长度: {len(data)} 字节")
            
            # 尝试解析JSON
            try:
                json_data = json.loads(message)
                if isinstance(json_data, list):
                    print(f"🚗 车辆数量: {len(json_data)}")
                    for i, vehicle in enumerate(json_data):
                        if isinstance(vehicle, dict) and 'device_id' in vehicle:
                            pos = vehicle.get('position', {})
                            rot = vehicle.get('rotation', {})
                            print(f"  车辆 {i+1}: {vehicle.get('device_id', 'Unknown')}")
                            print(f"    位置: ({pos.get('x', 0):.1f}, {pos.get('y', 0):.1f}, {pos.get('z', 0):.1f})")
                            print(f"    角度: ({rot.get('x', 0):.1f}, {rot.get('y', 0):.1f}, {rot.get('z', 0):.1f})")
                            print(f"    电量: {vehicle.get('power', 0)}%, 消耗: {vehicle.get('consume', 0)}")
                            print(f"    类型: {vehicle.get('type', 'Unknown')}")
                else:
                    print(f"📄 JSON数据: {json.dumps(json_data, ensure_ascii=False, indent=2)}")
                    
            except json.JSONDecodeError:
                print(f"📄 原始数据: {message}")
            
            print("-" * 80)
            
        except Exception as e:
            print(f"❌ 处理数据时发生错误: {e}")
    
    def cleanup(self):
        """清理资源"""
        self.running = False
        if self.socket:
            self.socket.close()
        print(f"\n✅ UDP服务端已关闭，共接收 {self.received_count} 条消息")
    
    def stop_server(self):
        """停止服务端"""
        self.running = False

def print_usage():
    """打印使用说明"""
    print("UDP服务端测试工具使用说明:")
    print("1. 默认监听 127.0.0.1:9092")
    print("2. 按 Ctrl+C 停止服务端")
    print("3. 可以修改代码中的 host 和 port 参数来改变监听地址")
    print()

def main():
    print("=" * 80)
    print("🔧 UDP服务端测试工具")
    print("=" * 80)
    print_usage()
    
    # 创建UDP服务端实例
    server = UdpTestServer()
    
    try:
        # 启动服务端
        server.start_server()
    except KeyboardInterrupt:
        print("\n⏹️  收到停止信号，正在关闭服务端...")
        server.stop_server()
    except Exception as e:
        print(f"❌ 服务端运行时发生错误: {e}")

if __name__ == "__main__":
    main()
